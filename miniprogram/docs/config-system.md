# 配置系统使用指南

## 📋 概述

小程序配置系统提供了统一的配置管理机制，支持从云端动态获取配置信息，实现灵活的功能控制和参数管理。

## 🏗️ 架构设计

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   小程序页面     │    │   配置管理器     │    │   云端配置API    │
│                 │    │ ConfigManager   │    │                 │
│ - 获取配置       │◄──►│ - 缓存管理       │◄──►│ - 配置存储       │
│ - 使用配置       │    │ - 降级处理       │    │ - 类型验证       │
│ - 实时更新       │    │ - 初始化         │    │ - 权限控制       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🔧 核心组件

### 1. ConfigManager (配置管理器)

位置：`miniprogram/core/managers/config-manager.js`

**主要功能：**
- 应用启动时自动加载云端配置
- 提供配置获取接口
- 配置缓存管理
- 降级处理机制

**使用方法：**
```javascript
import { getConfigManager } from '../core/managers/config-manager.js'

const configManager = getConfigManager()

// 初始化配置管理器
await configManager.initialize()

// 获取配置值
const statisticsConfig = configManager.get('statistics_page')
const defaultValue = configManager.get('non_existent_key', { enabled: false })

// 检查配置是否已加载
if (configManager.isLoaded()) {
  console.log('配置已加载完成')
}
```

### 2. ConfigApi (配置API)

位置：`miniprogram/core/api/modules/config.js`

**主要功能：**
- 与云函数通信
- 获取所有配置数据

**使用方法：**
```javascript
import { configApi } from '../core/api/modules/config.js'

// 获取所有配置
const result = await configApi.getAllConfigs()
if (result.success) {
  console.log('配置数据:', result.data)
}
```

## 📝 使用示例

### 1. 页面中使用配置

```javascript
// pages/statistics/index.js
import { getConfigManager } from '../../core/managers/config-manager.js'

Page({
  data: {
    pageConfig: null
  },

  async onLoad() {
    const configManager = getConfigManager()
    
    // 确保配置已初始化
    await configManager.initialize()
    
    // 获取统计页面配置
    const statisticsConfig = configManager.get('statistics_page', {
      enabled: true,
      title: '数据统计',
      message: '暂无数据'
    })
    
    this.setData({
      pageConfig: statisticsConfig
    })
    
    // 根据配置控制页面行为
    if (!statisticsConfig.enabled) {
      wx.showToast({
        title: statisticsConfig.message || '功能暂未开放',
        icon: 'none'
      })
      return
    }
  }
})
```

### 2. 组件中使用配置

```javascript
// components/community-modal/index.js
import { getConfigManager } from '../../core/managers/config-manager.js'

Component({
  data: {
    communityInfo: ''
  },

  lifetimes: {
    async attached() {
      const configManager = getConfigManager()
      await configManager.initialize()
      
      // 获取社群信息配置（Markdown格式）
      const communityConfig = configManager.get('community_group_info', '')
      
      this.setData({
        communityInfo: communityConfig
      })
    }
  }
})
```

### 3. 工具函数中使用配置

```javascript
// utils/feature-flags.js
import { getConfigManager } from '../core/managers/config-manager.js'

/**
 * 检查功能是否启用
 * @param {string} featureKey - 功能配置键
 * @returns {boolean} 是否启用
 */
export async function isFeatureEnabled(featureKey) {
  const configManager = getConfigManager()
  await configManager.initialize()
  
  const config = configManager.get(featureKey, { enabled: false })
  return config.enabled === true
}

/**
 * 获取功能配置
 * @param {string} featureKey - 功能配置键
 * @param {any} defaultConfig - 默认配置
 * @returns {any} 配置值
 */
export async function getFeatureConfig(featureKey, defaultConfig = {}) {
  const configManager = getConfigManager()
  await configManager.initialize()
  
  return configManager.get(featureKey, defaultConfig)
}
```

## 🎯 最佳实践

### 1. 配置键命名规范

```javascript
// 推荐的命名方式
'statistics_page'           // 功能页面配置
'user_vip_benefits'        // 用户功能配置
'system_maintenance_mode'   // 系统配置
'ui_theme_colors'          // 界面配置
'business_payment_config'   // 业务配置
```

### 2. 默认值设计

```javascript
// 总是提供合理的默认值
const config = configManager.get('feature_config', {
  enabled: false,           // 默认关闭新功能
  title: '功能标题',
  description: '功能描述',
  options: []
})
```

### 3. 错误处理

```javascript
try {
  const configManager = getConfigManager()
  await configManager.initialize()
  
  const config = configManager.get('my_config')
  // 使用配置...
} catch (error) {
  console.error('配置加载失败:', error)
  // 使用默认行为...
}
```

### 4. 性能优化

```javascript
// 在应用启动时预加载配置
// app.js
import { getConfigManager } from './core/managers/config-manager.js'

App({
  async onLaunch() {
    // 预加载配置
    const configManager = getConfigManager()
    await configManager.initialize()
    console.log('配置预加载完成')
  }
})
```

## 🔄 配置更新机制

### 1. 自动更新

配置管理器会在应用启动时自动从云端加载最新配置。

### 2. 手动刷新

```javascript
const configManager = getConfigManager()

// 重新加载配置
await configManager.loadCloudConfigs()
```

### 3. 缓存策略

- 配置在内存中缓存，应用重启时重新加载
- 网络异常时使用默认配置
- 支持降级处理机制

## 🛡️ 安全考虑

### 1. 敏感信息

- 不要在配置中存储敏感信息（如密钥、密码）
- 使用环境变量或服务端配置管理敏感数据

### 2. 配置验证

- 云函数会验证配置数据类型
- 客户端应对配置值进行合理性检查

### 3. 权限控制

- 配置的读取不需要特殊权限
- 配置的修改需要管理员权限

## 📊 监控和调试

### 1. 调试信息

```javascript
const configManager = getConfigManager()

// 获取所有配置
const allConfigs = configManager.getAll()
console.log('当前所有配置:', allConfigs)

// 检查初始化状态
console.log('配置是否已加载:', configManager.isLoaded())
```

### 2. 错误日志

配置管理器会自动记录关键操作的日志，便于问题排查。

## 🚀 扩展功能

### 1. 配置监听

```javascript
// 未来可扩展配置变更监听
configManager.on('configChanged', (key, newValue, oldValue) => {
  console.log(`配置 ${key} 已更新:`, { newValue, oldValue })
})
```

### 2. 条件配置

```javascript
// 根据用户类型获取不同配置
const userType = getCurrentUserType()
const config = configManager.get(`feature_${userType}`, defaultConfig)
```

## 📋 注意事项

1. **初始化时机**：确保在使用配置前调用 `initialize()`
2. **默认值**：总是提供合理的默认值
3. **错误处理**：妥善处理网络异常和配置缺失
4. **性能考虑**：避免频繁调用配置API
5. **数据类型**：注意配置值的数据类型，特别是JSON和数组类型

通过合理使用配置系统，可以实现灵活的功能控制和参数管理，提升应用的可维护性和用户体验。
