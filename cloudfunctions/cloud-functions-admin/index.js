/**
 * 管理端云函数入口
 * 支持HTTP API调用，使用SECRET_KEY验证
 */

const cloud = require("wx-server-sdk");
const { validateApiPermission, logAdminOperation } = require('./middleware/auth');
const { error, success } = require('./utils/response');

// 初始化云开发
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV,
});

// 初始化数据库
const db = cloud.database();

// 导入管理端API模块
const { 
  createAnnouncement, 
  updateAnnouncement, 
  deleteAnnouncement, 
  getAnnouncementListAdmin, 
  getAnnouncementStats 
} = require('./api/announcements-admin');

const {
  createConfig,
  updateConfig,
  deleteConfig,
  getAllConfigsAdmin,
  getConfigStats,
  getConfigTypes
} = require('./api/config-admin');

const {
  getUserList,
  getUserDetail,
  updateUserStatus,
  getUserStats,
  exportUserData,
  getVipUsersAdmin,
  getVipRecordsAdmin,
  getVipStatsAdmin,
  grantVipAdmin,
  revokeVipAdmin,
  getUserPointsHistoryAdmin,
  deleteUser
} = require('./api/user-admin');

const {
  getFeedbackListAdmin,
  replyFeedback,
  updateFeedbackStatus,
  getFeedbackStatsAdmin,
  exportFeedbackData,
  batchOperateFeedback
} = require('./api/feedback-admin');

const {
  getPointsStatsAdmin,
  adjustUserPoints,
  getPointsRecordsAdmin,
  exportPointsData,
  getPointsLeaderboard,
  batchAdjustPoints,
  getPointsConfig,
  updatePointsConfig
} = require('./api/points-admin');

// 系统管理API
const {
  getSystemStats,
  getDashboardStats,
  getTrendData,
  getDetailedStats,
  getSystemHealth,
  clearSystemCache,
  getSystemLogs
} = require('./api/system-admin');

const { 
  createStoreItem, 
  updateStoreItem, 
  deleteStoreItem, 
  getStoreItemListAdmin, 
  getStoreStatsAdmin, 
  createRedemptionCode, 
  getRedemptionCodeListAdmin 
} = require('./api/store-admin');

const {
  getCheckInStatsAdmin,
  getUserCheckInHistoryAdmin,
  exportCheckInData,
  getUserCheckInHistoryWithStatsAdmin
} = require('./api/check-in-admin');

const {
  getAllUserDataAdmin,
  getUserDataStatsAdmin,
  cleanupUserDataAdmin,
  exportAllDataAdmin
} = require('./api/user-data-admin');

// 友情应用管理API
const {
  createFriendApp,
  updateFriendApp,
  deleteFriendApp,
  getFriendAppListAdmin,
  getFriendAppStatsAdmin,
  toggleFriendAppStatus,
  recordFriendAppClick,
  batchUpdateFriendAppOrder
} = require('./api/friend-apps-admin');

// 摸鱼状态管理API
const {
  getFishingStatsAdmin,
  getFishingLeaderboard,
  getFishingRecordsAdmin,
  cleanupExpiredFishing
} = require('./api/fishing-admin');

/**
 * 管理端云函数主入口
 * @param {Object} event - 事件对象
 * @param {string} event.type - API类型
 * @param {Object} event.data - API数据
 * @param {string} event.secretKey - 访问密钥
 * @param {Object} context - 上下文对象
 */
exports.main = async (event, context) => {
  console.info(`==================== 管理端云函数被调用：`, {
    type: event.type,
    hasSecretKey: !!event.secretKey,
    dataKeys: event.data ? Object.keys(event.data) : []
  });

  try {
    // 验证必要参数
    if (!event.type) {
      return error('缺少API类型参数', 'MISSING_TYPE');
    }

    // 验证API权限（包含SECRET_KEY验证）
    const permissionResult = validateApiPermission(event.type, event);
    if (!permissionResult.success) {
      return permissionResult;
    }

    // 记录管理操作
    logAdminOperation(event.type, event.data, event.secretKey);

    // 路由到具体的API处理函数
    const apiData = event.data || {};
    
    switch (event.type) {
      // ==================== 公告管理 ====================
      case "createAnnouncement":
        return await createAnnouncement(apiData);
      case "updateAnnouncement":
        return await updateAnnouncement(apiData);
      case "deleteAnnouncement":
        return await deleteAnnouncement(apiData);
      case "getAnnouncementListAdmin":
        return await getAnnouncementListAdmin(apiData);
      case "getAnnouncementStats":
        return await getAnnouncementStats(apiData);

      // ==================== 配置管理 ====================
      case "createConfig":
        return await createConfig(apiData);
      case "updateConfig":
        return await updateConfig(apiData);
      case "deleteConfig":
        return await deleteConfig(apiData);
      case "getAllConfigsAdmin":
        return await getAllConfigsAdmin(apiData);
      case "getConfigStats":
        return await getConfigStats(apiData);
      case "getConfigTypes":
        return await getConfigTypes(apiData);

      // ==================== 用户管理 ====================
      case "getUserList":
        return await getUserList(apiData);
      case "getUserDetail":
        return await getUserDetail(apiData);
      case "updateUserStatus":
        return await updateUserStatus(apiData);
      case "updateUserAdmin":
        return await updateUserStatus(apiData);
      case "getUserStats":
        return await getUserStats(apiData);
      case "exportUserData":
        return await exportUserData(apiData);
      case "deleteUser":
        return await deleteUser(apiData);

      // ==================== VIP管理 ====================
      case "getVipUsersAdmin":
        return await getVipUsersAdmin(apiData);
      case "getVipRecordsAdmin":
        return await getVipRecordsAdmin(apiData);
      case "getVipStatsAdmin":
        return await getVipStatsAdmin(apiData);
      case "grantVipAdmin":
        return await grantVipAdmin(apiData);
      case "revokeVipAdmin":
        return await revokeVipAdmin(apiData);

      // ==================== 用户历史记录 ====================
      case "getUserPointsHistoryAdmin":
        return await getUserPointsHistoryAdmin(apiData);

      // ==================== 反馈管理 ====================
      case "getFeedbackListAdmin":
        return await getFeedbackListAdmin(apiData);
      case "replyFeedback":
        return await replyFeedback(apiData);
      case "updateFeedbackStatus":
        return await updateFeedbackStatus(apiData);
      case "getFeedbackStatsAdmin":
        return await getFeedbackStatsAdmin(apiData);
      case "exportFeedbackData":
        return await exportFeedbackData(apiData);
      case "batchOperateFeedback":
        return await batchOperateFeedback(apiData);

      // ==================== 积分管理 ====================
      case "getPointsStatsAdmin":
        return await getPointsStatsAdmin(apiData);
      case "adjustUserPoints":
        return await adjustUserPoints(apiData);
      case "getPointsRecordsAdmin":
        return await getPointsRecordsAdmin(apiData);
      case "exportPointsData":
        return await exportPointsData(apiData);
      case "getPointsLeaderboard":
        return await getPointsLeaderboard(apiData);
      case "batchAdjustPoints":
        return await batchAdjustPoints(apiData);
      case "getPointsConfig":
        return await getPointsConfig(apiData);
      case "updatePointsConfig":
        return await updatePointsConfig(apiData);

      // ==================== 商店管理 ====================
      case "createStoreItem":
        return await createStoreItem(apiData);
      case "updateStoreItem":
        return await updateStoreItem(apiData);
      case "deleteStoreItem":
        return await deleteStoreItem(apiData);
      case "getStoreItemListAdmin":
        return await getStoreItemListAdmin(apiData);
      case "getStoreStatsAdmin":
        return await getStoreStatsAdmin(apiData);
      case "createRedemptionCode":
        return await createRedemptionCode(apiData);
      case "getRedemptionCodeListAdmin":
        return await getRedemptionCodeListAdmin(apiData);

      // ==================== 签到管理 ====================
      case "getCheckInStatsAdmin":
        return await getCheckInStatsAdmin(apiData);
      case "getUserCheckInHistoryAdmin":
        return await getUserCheckInHistoryAdmin(apiData);
      case "getUserCheckInHistoryWithStatsAdmin":
        return await getUserCheckInHistoryWithStatsAdmin(apiData);
      case "exportCheckInData":
        return await exportCheckInData(apiData);

      // ==================== 数据管理 ====================
      case "getAllUserDataAdmin":
        return await getAllUserDataAdmin(apiData);
      case "getUserDataStatsAdmin":
        return await getUserDataStatsAdmin(apiData);
      case "cleanupUserDataAdmin":
        return await cleanupUserDataAdmin(apiData);
      case "exportAllDataAdmin":
        return await exportAllDataAdmin(apiData);

      // ==================== 友情应用管理 ====================
      case "createFriendApp":
        return await createFriendApp(apiData);
      case "updateFriendApp":
        return await updateFriendApp(apiData);
      case "deleteFriendApp":
        return await deleteFriendApp(apiData);
      case "getFriendAppListAdmin":
        return await getFriendAppListAdmin(apiData);
      case "getFriendAppStatsAdmin":
        return await getFriendAppStatsAdmin(apiData);
      case "toggleFriendAppStatus":
        return await toggleFriendAppStatus(apiData);
      case "recordFriendAppClick":
        return await recordFriendAppClick(apiData);
      case "batchUpdateFriendAppOrder":
        return await batchUpdateFriendAppOrder(apiData);

      // ==================== 签到管理扩展 ====================
      case "getCheckInLeaderboard":
        return await getCheckInLeaderboard(apiData);
      case "getCheckInConfig":
        return await getCheckInConfig(apiData);
      case "updateCheckInConfig":
        return await updateCheckInConfig(apiData);

      // ==================== 摸鱼状态管理 ====================
      case "getFishingRecordsAdmin":
        return await getFishingRecordsAdmin(apiData);
      case "getFishingStatsAdmin":
        return await getFishingStatsAdmin(apiData);
      case "getFishingLeaderboard":
        return await getFishingLeaderboard(apiData);
      case "cleanupExpiredFishing":
        return await cleanupExpiredFishing(apiData);

      // ==================== 缓存管理 ====================
      case "getCacheListAdmin":
        return await getCacheListAdmin(apiData);
      case "getCacheStatsAdmin":
        return await getCacheStatsAdmin(apiData);
      case "getCacheConfigAdmin":
        return await getCacheConfigAdmin(apiData);
      case "updateCacheConfigAdmin":
        return await updateCacheConfigAdmin(apiData);
      case "cleanupExpiredCacheAdmin":
        return await cleanupExpiredCacheAdmin(apiData);
      case "clearAllCacheAdmin":
        return await clearAllCacheAdmin(apiData);
      case "refreshCacheAdmin":
        return await refreshCacheAdmin(apiData);
      case "deleteCacheAdmin":
        return await deleteCacheAdmin(apiData);
      case "batchRefreshCacheAdmin":
        return await batchRefreshCacheAdmin(apiData);
      case "batchDeleteCacheAdmin":
        return await batchDeleteCacheAdmin(apiData);

      // ==================== 系统管理 ====================
      case "getSystemStats":
        return await getSystemStats(apiData);
      case "getDashboardStats":
        return await getDashboardStats(apiData);
      case "getTrendData":
        return await getTrendData(apiData);
      case "getDetailedStats":
        return await getDetailedStats(apiData);
      case "getSystemHealth":
        return await getSystemHealth(apiData);
      case "clearSystemCache":
        return await clearSystemCache(apiData);
      case "getSystemLogs":
        return await getSystemLogs(apiData);

      default:
        return error(`未知的API类型: ${event.type}`, 'UNKNOWN_API_TYPE');
    }

  } catch (err) {
    console.error('管理端云函数执行失败:', err);
    return error(err.message || '服务器内部错误', 'SERVER_ERROR');
  }
};




