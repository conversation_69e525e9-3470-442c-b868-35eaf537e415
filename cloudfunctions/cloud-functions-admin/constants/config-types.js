/**
 * 配置数据类型常量定义
 */

/**
 * 支持的配置数据类型
 */
const CONFIG_DATA_TYPES = {
  STRING: 'string',
  TEXT: 'text',
  MARKDOWN: 'markdown',
  NUMBER: 'number',
  BOOLEAN: 'boolean',
  JSON: 'json',
  ARRAY: 'array',
  PASSWORD: 'password',
  URL: 'url',
  EMAIL: 'email'
}

/**
 * 数据类型列表（用于验证）
 */
const VALID_DATA_TYPES = Object.values(CONFIG_DATA_TYPES)

/**
 * 数据类型配置信息
 */
const DATA_TYPE_CONFIG = {
  [CONFIG_DATA_TYPES.STRING]: {
    name: '字符串',
    description: '单行文本输入',
    icon: 'Type',
    inputType: 'text',
    multiline: false
  },
  [CONFIG_DATA_TYPES.TEXT]: {
    name: '多行文本',
    description: '多行文本输入',
    icon: 'FileText',
    inputType: 'textarea',
    multiline: true
  },
  [CONFIG_DATA_TYPES.MARKDOWN]: {
    name: 'Markdown',
    description: 'Markdown格式文本',
    icon: 'FileCode',
    inputType: 'markdown',
    multiline: true
  },
  [CONFIG_DATA_TYPES.NUMBER]: {
    name: '数字',
    description: '数值输入',
    icon: 'Hash',
    inputType: 'number',
    multiline: false
  },
  [CONFIG_DATA_TYPES.BOOLEAN]: {
    name: '布尔值',
    description: '开关选择',
    icon: 'ToggleLeft',
    inputType: 'switch',
    multiline: false
  },
  [CONFIG_DATA_TYPES.JSON]: {
    name: 'JSON',
    description: 'JSON格式数据',
    icon: 'Braces',
    inputType: 'json',
    multiline: true
  },
  [CONFIG_DATA_TYPES.ARRAY]: {
    name: '数组',
    description: '数组格式数据',
    icon: 'List',
    inputType: 'array',
    multiline: true
  },
  [CONFIG_DATA_TYPES.PASSWORD]: {
    name: '密码',
    description: '密码输入',
    icon: 'Lock',
    inputType: 'password',
    multiline: false
  },
  [CONFIG_DATA_TYPES.URL]: {
    name: 'URL',
    description: '网址链接',
    icon: 'Link',
    inputType: 'url',
    multiline: false
  },
  [CONFIG_DATA_TYPES.EMAIL]: {
    name: '邮箱',
    description: '邮箱地址',
    icon: 'Mail',
    inputType: 'email',
    multiline: false
  }
}

/**
 * 配置分类
 */
const CONFIG_CATEGORIES = {
  GENERAL: 'general',
  SYSTEM: 'system',
  FEATURE: 'feature',
  UI: 'ui',
  BUSINESS: 'business'
}

/**
 * 分类配置信息
 */
const CATEGORY_CONFIG = {
  [CONFIG_CATEGORIES.GENERAL]: {
    name: '通用配置',
    description: '通用系统配置',
    icon: 'Settings',
    color: '#409EFF'
  },
  [CONFIG_CATEGORIES.SYSTEM]: {
    name: '系统配置',
    description: '系统核心配置',
    icon: 'Server',
    color: '#E6A23C'
  },
  [CONFIG_CATEGORIES.FEATURE]: {
    name: '功能配置',
    description: '功能模块配置',
    icon: 'Zap',
    color: '#67C23A'
  },
  [CONFIG_CATEGORIES.UI]: {
    name: '界面配置',
    description: '用户界面配置',
    icon: 'Palette',
    color: '#909399'
  },
  [CONFIG_CATEGORIES.BUSINESS]: {
    name: '业务配置',
    description: '业务逻辑配置',
    icon: 'Briefcase',
    color: '#F56C6C'
  }
}

/**
 * 获取数据类型选项列表
 */
function getDataTypeOptions() {
  return VALID_DATA_TYPES.map(type => ({
    value: type,
    label: DATA_TYPE_CONFIG[type].name,
    description: DATA_TYPE_CONFIG[type].description,
    icon: DATA_TYPE_CONFIG[type].icon
  }))
}

/**
 * 获取分类选项列表
 */
function getCategoryOptions() {
  return Object.values(CONFIG_CATEGORIES).map(category => ({
    value: category,
    label: CATEGORY_CONFIG[category].name,
    description: CATEGORY_CONFIG[category].description,
    icon: CATEGORY_CONFIG[category].icon,
    color: CATEGORY_CONFIG[category].color
  }))
}

module.exports = {
  CONFIG_DATA_TYPES,
  VALID_DATA_TYPES,
  DATA_TYPE_CONFIG,
  CONFIG_CATEGORIES,
  CATEGORY_CONFIG,
  getDataTypeOptions,
  getCategoryOptions
}
