/**
 * 配置管理API（管理端）
 * 提供完整的配置CRUD管理功能
 */

const { success, error, paginated, statsData, invalidParam, notFound } = require('../utils/response')
const { wrapAsync } = require('../utils/async-wrapper')
const { validateRequired, validatePagination, validateSorting, validateStringLength, validateEnum } = require('../utils/validators')

// 使用本地的数据库操作类
const configDB = require('../db/config')
const { VALID_DATA_TYPES, CONFIG_CATEGORIES, getDataTypeOptions, getCategoryOptions } = require('../constants/config-types')

/**
 * 创建配置（管理端）
 */
exports.createConfig = wrapAsync(async (params = {}) => {
  // 验证必填参数
  const validation = validateRequired(params, ['key', 'value'])
  if (!validation.success) {
    return validation
  }

  const { key, value, description, category, dataType, enable } = params

  // 验证配置键格式
  const keyValidation = validateStringLength(key, 'key', 1, 100)
  if (!keyValidation.success) {
    return keyValidation
  }

  // 验证配置键只能包含字母、数字、下划线和点
  if (!/^[a-zA-Z0-9._]+$/.test(key)) {
    return invalidParam('key', '配置键只能包含字母、数字、下划线和点')
  }

  // 验证描述长度
  if (description && description.length > 500) {
    return invalidParam('description', '描述不能超过500字符')
  }

  // 验证分类
  const validCategories = Object.values(CONFIG_CATEGORIES)
  if (category && !validCategories.includes(category)) {
    return invalidParam('category', `分类必须是: ${validCategories.join(', ')}`)
  }

  // 验证数据类型
  if (dataType && !VALID_DATA_TYPES.includes(dataType)) {
    return invalidParam('dataType', `数据类型必须是: ${VALID_DATA_TYPES.join(', ')}`)
  }

  try {
    // 检查配置键是否已存在
    const existingResult = await configDB.getConfig(key)
    if (existingResult.success && existingResult.data) {
      return error('配置键已存在', 'CONFIG_KEY_EXISTS')
    }

    // 验证值的数据类型
    const finalDataType = dataType || 'string'
    const validatedValue = validateConfigValue(value, finalDataType)
    if (!validatedValue.success) {
      return validatedValue
    }

    const configData = {
      key: key.trim(),
      value: validatedValue.data,
      description: description ? description.trim() : '',
      category: category || 'general',
      dataType: finalDataType,
      enable: enable !== false // 默认启用
    }

    const result = await configDB.createConfig(configData)
    
    if (!result.success) {
      return error(result.message || '创建配置失败')
    }

    return success(result.data, '配置创建成功')
  } catch (err) {
    console.error('创建配置失败:', err)
    return error('创建配置失败')
  }
})

/**
 * 更新配置（管理端）
 */
exports.updateConfig = wrapAsync(async (params = {}) => {
  // 验证必填参数
  const validation = validateRequired(params, ['key'])
  if (!validation.success) {
    return validation
  }

  const { key, value, description, category, dataType, enable } = params

  try {
    // 检查配置是否存在
    const existingResult = await configDB.getConfig(key)
    if (!existingResult.success || !existingResult.data) {
      return notFound('配置')
    }

    const existingConfig = existingResult.data

    // 构建更新数据
    const updateData = {}
    
    if (value !== undefined) {
      const targetDataType = dataType || existingConfig.dataType || 'string'
      const validatedValue = validateConfigValue(value, targetDataType)
      if (!validatedValue.success) {
        return validatedValue
      }
      updateData.value = validatedValue.data
    }
    
    if (description !== undefined) {
      if (description.length > 500) {
        return invalidParam('description', '描述不能超过500字符')
      }
      updateData.description = description.trim()
    }
    
    if (category !== undefined) {
      const validCategories = Object.values(CONFIG_CATEGORIES)
      if (!validCategories.includes(category)) {
        return invalidParam('category', `分类必须是: ${validCategories.join(', ')}`)
      }
      updateData.category = category
    }

    if (dataType !== undefined) {
      if (!VALID_DATA_TYPES.includes(dataType)) {
        return invalidParam('dataType', `数据类型必须是: ${VALID_DATA_TYPES.join(', ')}`)
      }
      updateData.dataType = dataType
    }
    
    if (enable !== undefined) {
      updateData.enable = enable
    }

    const result = await configDB.updateConfig(key, updateData)
    
    if (!result.success) {
      return error(result.message || '更新配置失败')
    }

    return success(result.data, '配置更新成功')
  } catch (err) {
    console.error('更新配置失败:', err)
    return error('更新配置失败')
  }
})

/**
 * 删除配置（管理端）
 */
exports.deleteConfig = wrapAsync(async (params = {}) => {
  // 验证必填参数
  const validation = validateRequired(params, ['key'])
  if (!validation.success) {
    return validation
  }

  const { key } = params

  try {
    // 检查配置是否存在
    const existingResult = await configDB.getConfig(key)
    if (!existingResult.success || !existingResult.data) {
      return notFound('配置')
    }

    const result = await configDB.deleteConfig(key)
    
    if (!result.success) {
      return error(result.message || '删除配置失败')
    }

    return success(null, '配置删除成功')
  } catch (err) {
    console.error('删除配置失败:', err)
    return error('删除配置失败')
  }
})

/**
 * 获取所有配置（管理端）
 */
exports.getAllConfigsAdmin = wrapAsync(async (params = {}) => {
  const {
    category = 'all',
    enable = 'all',
    page = 1,
    pageSize = 50,
    sortBy = 'key',
    sortOrder = 'asc',
    keyword = ''
  } = params

  // 验证分页参数
  const paginationValidation = validatePagination(params, 100)
  if (!paginationValidation.success) {
    return paginationValidation
  }

  // 验证排序参数
  const allowedSortFields = ['key', 'category', 'dataType', 'enable', 'updatedAt']
  const sortingValidation = validateSorting(params, allowedSortFields)
  if (!sortingValidation.success) {
    return sortingValidation
  }

  try {
    const result = await configDB.getAllConfigsAdmin({
      category,
      enable,
      keyword,
      sortBy,
      sortOrder,
      limit: paginationValidation.data.limit,
      skip: paginationValidation.data.skip
    })

    if (!result.success) {
      return error(result.message || '获取配置列表失败')
    }

    return paginated(
      result.data.list,
      result.data.total,
      page,
      pageSize,
      '获取配置列表成功'
    )
  } catch (err) {
    console.error('获取配置列表失败:', err)
    return error('获取配置列表失败')
  }
})

/**
 * 获取配置统计（管理端）
 */
exports.getConfigStats = wrapAsync(async (params = {}) => {
  try {
    const result = await configDB.getConfigStats()
    
    if (!result.success) {
      return error(result.message || '获取配置统计失败')
    }

    return statsData(result.data, '获取配置统计成功')
  } catch (err) {
    console.error('获取配置统计失败:', err)
    return error('获取配置统计失败')
  }
})

/**
 * 验证配置值的数据类型
 * @param {*} value - 配置值
 * @param {string} dataType - 数据类型
 * @returns {Object} 验证结果
 */
function validateConfigValue(value, dataType) {
  try {
    switch (dataType) {
      case 'string':
        if (typeof value !== 'string') {
          return invalidParam('value', '值必须是字符串类型')
        }
        return { success: true, data: value }

      case 'text':
        if (typeof value !== 'string') {
          return invalidParam('value', '值必须是字符串类型')
        }
        return { success: true, data: value }

      case 'markdown':
        if (typeof value !== 'string') {
          return invalidParam('value', '值必须是字符串类型')
        }
        return { success: true, data: value }

      case 'password':
        if (typeof value !== 'string') {
          return invalidParam('value', '值必须是字符串类型')
        }
        if (value.length < 6) {
          return invalidParam('value', '密码长度不能少于6位')
        }
        return { success: true, data: value }

      case 'url':
        if (typeof value !== 'string') {
          return invalidParam('value', '值必须是字符串类型')
        }
        try {
          new URL(value)
          return { success: true, data: value }
        } catch (err) {
          return invalidParam('value', '值必须是有效的URL格式')
        }

      case 'email':
        if (typeof value !== 'string') {
          return invalidParam('value', '值必须是字符串类型')
        }
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        if (!emailRegex.test(value)) {
          return invalidParam('value', '值必须是有效的邮箱格式')
        }
        return { success: true, data: value }

      case 'number':
        const numValue = Number(value)
        if (isNaN(numValue)) {
          return invalidParam('value', '值必须是有效数字')
        }
        return { success: true, data: numValue }

      case 'boolean':
        if (typeof value === 'boolean') {
          return { success: true, data: value }
        }
        if (typeof value === 'string') {
          const lowerValue = value.toLowerCase()
          if (lowerValue === 'true') {
            return { success: true, data: true }
          }
          if (lowerValue === 'false') {
            return { success: true, data: false }
          }
        }
        return invalidParam('value', '值必须是布尔类型（true/false）')

      case 'json':
        if (typeof value === 'object') {
          return { success: true, data: value }
        }
        if (typeof value === 'string') {
          try {
            const jsonValue = JSON.parse(value)
            return { success: true, data: jsonValue }
          } catch (err) {
            return invalidParam('value', '值必须是有效的JSON格式')
          }
        }
        return invalidParam('value', '值必须是JSON对象')

      case 'array':
        if (Array.isArray(value)) {
          return { success: true, data: value }
        }
        if (typeof value === 'string') {
          try {
            const arrayValue = JSON.parse(value)
            if (Array.isArray(arrayValue)) {
              return { success: true, data: arrayValue }
            }
            return invalidParam('value', '值必须是数组格式')
          } catch (err) {
            return invalidParam('value', '值必须是有效的JSON数组格式')
          }
        }
        return invalidParam('value', '值必须是数组类型')

      default:
        return invalidParam('dataType', '不支持的数据类型')
    }
  } catch (err) {
    return invalidParam('value', '值验证失败')
  }
}

/**
 * 获取配置类型信息（管理端）
 */
exports.getConfigTypes = wrapAsync(async (params = {}) => {
  try {
    const data = {
      dataTypes: getDataTypeOptions(),
      categories: getCategoryOptions()
    }

    return success(data, '获取配置类型信息成功')
  } catch (err) {
    console.error('获取配置类型信息失败:', err)
    return error('获取配置类型信息失败')
  }
})
