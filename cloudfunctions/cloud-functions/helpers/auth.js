/**
 * 认证相关辅助函数
 */

const cloud = require('wx-server-sdk')
const usersDB = require('../db/users')

/**
 * 获取当前用户的openid
 * @returns {string} 用户openid
 */
function getCurrentOpenid() {
  const wxContext = cloud.getWXContext()
  return wxContext.OPENID
}

/**
 * 获取当前用户的unionid
 * @returns {string} 用户unionid
 */
function getCurrentUnionid() {
  const wxContext = cloud.getWXContext()
  return wxContext.UNIONID
}

/**
 * 确保用户存在，不存在则创建
 * @param {Object} userInfo - 用户信息
 * @returns {Promise<Object>} 操作结果
 */
async function ensureUserExists(userInfo = {}) {
  try {
    const openid = getCurrentOpenid()
    if (!openid) {
      return {
        success: false,
        message: '无法获取用户标识'
      }
    }

    // 检查用户是否已存在
    const existingResult = await usersDB.findByOpenid(openid)
    if (existingResult.success && existingResult.data) {
      console.log(`[用户验证] 用户已存在: ${openid}`)
      return {
        success: true,
        data: existingResult.data,
        isNewUser: false
      }
    }

    console.log(`[用户创建] 开始创建新用户: ${openid}`)

    // 创建新用户
    const createResult = await usersDB.createUser({
      openid,
      version: userInfo.version
    })

    if (!createResult.success) {
      // 检查是否是唯一索引冲突错误
      if (isUniqueConstraintError(createResult.message)) {
        console.log(`[用户创建] 检测到唯一索引冲突，重新查询用户: ${openid}`)

        // 可能是并发创建导致的冲突，重新查询用户
        const retryResult = await usersDB.findByOpenid(openid)
        if (retryResult.success && retryResult.data) {
          console.log(`[用户创建] 重新查询成功，用户已存在: ${openid}`)
          return {
            success: true,
            data: retryResult.data,
            isNewUser: false
          }
        } else {
          console.error(`[用户创建] 重新查询失败: ${openid}`, retryResult)
          return {
            success: false,
            message: '用户创建失败，请重试'
          }
        }
      }

      console.error(`[用户创建] 创建失败: ${openid}`, createResult)
      return createResult
    }

    console.log(`[用户创建] 创建成功: ${openid}, 用户编号: ${createResult.data.no}`)
    return {
      success: true,
      data: createResult.data,
      isNewUser: true
    }
  } catch (error) {
    console.error('确保用户存在失败:', error)

    // 如果是唯一索引冲突错误，尝试重新查询
    if (isUniqueConstraintError(error.message)) {
      try {
        const openid = getCurrentOpenid()
        console.log(`[用户创建] 异常处理：检测到唯一索引冲突，重新查询用户: ${openid}`)

        const retryResult = await usersDB.findByOpenid(openid)
        if (retryResult.success && retryResult.data) {
          console.log(`[用户创建] 异常处理：重新查询成功: ${openid}`)
          return {
            success: true,
            data: retryResult.data,
            isNewUser: false
          }
        }
      } catch (retryError) {
        console.error('重新查询用户失败:', retryError)
      }
    }

    return {
      success: false,
      message: error.message || '用户操作失败'
    }
  }
}

/**
 * 检查是否是唯一索引冲突错误
 * @param {string} errorMessage - 错误信息
 * @returns {boolean} 是否是唯一索引冲突
 */
function isUniqueConstraintError(errorMessage) {
  if (!errorMessage || typeof errorMessage !== 'string') {
    return false
  }

  const message = errorMessage.toLowerCase()

  // 检查常见的唯一索引冲突错误信息
  const uniqueErrorPatterns = [
    'duplicate key',
    'unique constraint',
    'duplicate entry',
    'already exists',
    'duplicatekey',
    'e11000', // MongoDB 重复键错误代码
    '重复',
    '已存在'
  ]

  return uniqueErrorPatterns.some(pattern => message.includes(pattern))
}

/**
 * 验证VIP会员权限
 * @param {string} featureName - 功能名称
 * @param {string} openid - 用户openid，不传则使用当前用户
 * @returns {Promise<Object>} 验证结果
 */
async function validateVipPermission(featureName = '此功能', openid = null) {
  try {
    const { checkVipMembership } = require('./users')
    const isVipMember = await checkVipMembership(openid)

    if (!isVipMember) {
      return {
        success: false,
        message: `${featureName}仅限VIP会员使用`,
        needUpgrade: true,
        vipRequired: true
      }
    }

    return {
      success: true,
      message: '权限验证通过'
    }
  } catch (error) {
    console.error('验证VIP权限失败:', error)
    return {
      success: false,
      message: error.message || '权限验证失败'
    }
  }
}

/**
 * 检查用户是否为管理员
 * @param {string} openid - 用户openid，不传则使用当前用户
 * @returns {Promise<boolean>} 是否为管理员
 */
async function checkAdminPermission(openid = null) {
  try {
    const targetOpenid = openid || getCurrentOpenid()
    if (!targetOpenid) {
      return false
    }

    const userResult = await usersDB.findByOpenid(targetOpenid)
    if (!userResult.success || !userResult.data) {
      return false
    }

    return userResult.data.isAdmin === true
  } catch (error) {
    console.error('检查管理员权限失败:', error)
    return false
  }
}

/**
 * 验证管理员权限
 * @param {string} openid - 用户openid，不传则使用当前用户
 * @returns {Promise<Object>} 验证结果
 */
async function validateAdminPermission(openid = null) {
  try {
    const isAdmin = await checkAdminPermission(openid)

    if (!isAdmin) {
      return {
        success: false,
        message: '操作失败', // 不暴露权限不足信息
        needAdmin: true
      }
    }

    return {
      success: true,
      message: '管理员权限验证通过'
    }
  } catch (error) {
    console.error('验证管理员权限失败:', error)
    return {
      success: false,
      message: '操作失败'
    }
  }
}

module.exports = {
  getCurrentOpenid,
  getCurrentUnionid,
  ensureUserExists,
  validateVipPermission,
  checkAdminPermission,
  validateAdminPermission,
  isUniqueConstraintError
}
