<template>
  <div class="settings-container">
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">
          <SettingsIcon :size="24" />
          系统设置
        </h1>
        <p class="page-description">管理系统基础设置和微信配置</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="refreshData">
          <template #icon>
            <RefreshCwIcon :size="16" />
          </template>
          刷新
        </el-button>
        <el-button type="success" @click="goToConfigManagement">
          <template #icon>
            <SettingsIcon :size="16" />
          </template>
          配置管理
        </el-button>
      </div>
    </div>

    <!-- 系统状态 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon wechat">
          <CloudIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ connectionStatus }}</div>
          <div class="stat-label">API状态</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon enabled">
          <CheckCircleIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ systemStore.apiConnected ? '正常' : '异常' }}</div>
          <div class="stat-label">连接状态</div>
        </div>
      </div>
    </div>

    <!-- 微信配置卡片 -->
    <div class="config-section">
      <div class="section-header">
        <h3>微信配置</h3>
        <el-button type="primary" @click="showWechatConfig">
          <template #icon>
            <EditIcon :size="16" />
          </template>
          修改配置
        </el-button>
      </div>
      <div class="wechat-config-card">
        <div class="config-item">
          <label>AppID:</label>
          <span>{{ maskedAppId }}</span>
        </div>
        <div class="config-item">
          <label>云环境ID:</label>
          <span>{{ wechatConfig.env || '未配置' }}</span>
        </div>
        <div class="config-item">
          <label>云函数名称:</label>
          <span>{{ wechatConfig.functionName || '未配置' }}</span>
        </div>
        <div class="config-item">
          <label>连接状态:</label>
          <el-tag :type="systemStore.apiConnected ? 'success' : 'danger'">
            {{ systemStore.apiConnected ? '已连接' : '未连接' }}
          </el-tag>
        </div>
      </div>
    </div>

    <!-- Access Token 状态监控 -->
    <TokenStatus />


  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Settings as SettingsIcon,
  CheckCircle as CheckCircleIcon,
  Cloud as CloudIcon,
  RefreshCw as RefreshCwIcon
} from 'lucide-vue-next'
import { useSystemStore } from '@/stores/system.js'
import { callCloudFunction } from '@/api/wechat-api.js'
import { getWechatConfig } from '@/api/wechat-api.js'
import TokenStatus from '@/components/TokenStatus.vue'

const router = useRouter()
const systemStore = useSystemStore()

// 注册组件
defineOptions({
  components: {
    TokenStatus
  }
})

// 响应式数据
const loading = ref(false)

// 计算属性
const wechatConfig = computed(() => getWechatConfig())

const maskedAppId = computed(() => {
  const appId = wechatConfig.value.appId
  if (!appId) return '未配置'
  return appId.slice(0, 4) + '****' + appId.slice(-4)
})

const connectionStatus = computed(() => {
  return systemStore.apiConnected ? '已连接' : '未连接'
})

// 方法
async function refreshData() {
  // 刷新系统状态
  ElMessage.success('数据已刷新')
}

function goToConfigManagement() {
  router.push('/config-management')
}



function showWechatConfig() {
  router.push('/setup')
}





// 生命周期
onMounted(() => {
  // 页面初始化
})
</script>

<style scoped>
.settings-container {
  padding: 24px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-description {
  color: #6b7280;
  margin: 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-icon.total { background: #3b82f6; }
.stat-icon.enabled { background: #10b981; }
.stat-icon.disabled { background: #ef4444; }
.stat-icon.wechat { background: #8b5cf6; }

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.stat-label {
  font-size: 14px;
  color: #6b7280;
}

.config-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h3 {
  margin: 0;
  color: #1f2937;
}

.wechat-config-card {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.config-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.config-item label {
  font-weight: 500;
  color: #374151;
  min-width: 100px;
}

.filter-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.table-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.config-value {
  max-width: 300px;
  word-break: break-all;
}

.json-value {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 8px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
  color: #495057;
  white-space: pre-wrap;
  max-height: 200px;
  overflow-y: auto;
  margin: 0;
}

.text-value {
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
