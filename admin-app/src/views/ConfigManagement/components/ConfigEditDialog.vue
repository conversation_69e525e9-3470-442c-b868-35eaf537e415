<template>
  <el-dialog
    v-model="visible"
    :title="isEditing ? '编辑配置' : '新增配置'"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      class="config-form"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="配置键" prop="key">
            <el-input
              v-model="form.key"
              :disabled="isEditing"
              placeholder="请输入配置键，如：statistics_page"
              maxlength="50"
              show-word-limit
            />
            <div class="form-tip">
              配置键用于唯一标识配置项，创建后不可修改
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="数据类型" prop="dataType">
            <el-select
              v-model="form.dataType"
              placeholder="请选择数据类型"
              style="width: 100%"
              @change="handleDataTypeChange"
            >
              <el-option
                v-for="type in dataTypeOptions"
                :key="type.value"
                :label="type.label"
                :value="type.value"
              >
                <div class="type-option">
                  <span class="type-name">{{ type.label }}</span>
                  <span class="type-desc">{{ type.description }}</span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="分类" prop="category">
            <el-select
              v-model="form.category"
              placeholder="请选择分类"
              style="width: 100%"
            >
              <el-option
                v-for="category in categoryOptions"
                :key="category.value"
                :label="category.label"
                :value="category.value"
              >
                <div class="category-option">
                  <span class="category-name">{{ category.label }}</span>
                  <span class="category-desc">{{ category.description }}</span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="启用状态">
            <el-switch
              v-model="form.enable"
              active-text="启用"
              inactive-text="禁用"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="描述" prop="description">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="3"
          placeholder="请输入配置描述"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="配置值" prop="value">
        <!-- 字符串类型 -->
        <el-input
          v-if="form.dataType === 'string'"
          v-model="form.value"
          placeholder="请输入字符串值"
          maxlength="500"
          show-word-limit
        />

        <!-- 多行文本类型 -->
        <el-input
          v-else-if="form.dataType === 'text'"
          v-model="form.value"
          type="textarea"
          :rows="6"
          placeholder="请输入多行文本内容"
          maxlength="2000"
          show-word-limit
        />

        <!-- Markdown类型 -->
        <div v-else-if="form.dataType === 'markdown'" class="markdown-editor">
          <el-input
            v-model="form.value"
            type="textarea"
            :rows="8"
            placeholder="请输入Markdown内容"
            maxlength="5000"
            show-word-limit
          />
          <div class="markdown-preview" v-if="form.value">
            <div class="preview-title">预览效果：</div>
            <div class="preview-content" v-html="renderedMarkdown"></div>
          </div>
        </div>

        <!-- 数字类型 -->
        <el-input-number
          v-else-if="form.dataType === 'number'"
          v-model="form.value"
          placeholder="请输入数字"
          style="width: 100%"
          :precision="2"
        />

        <!-- 布尔类型 -->
        <el-radio-group
          v-else-if="form.dataType === 'boolean'"
          v-model="form.value"
        >
          <el-radio :label="true">是 (true)</el-radio>
          <el-radio :label="false">否 (false)</el-radio>
        </el-radio-group>

        <!-- JSON类型 -->
        <div v-else-if="form.dataType === 'json'" class="json-editor">
          <el-input
            v-model="jsonString"
            type="textarea"
            :rows="8"
            placeholder="请输入JSON格式的数据"
            @input="handleJsonInput"
          />
          <div class="json-validation" :class="{ error: jsonError }">
            {{ jsonError || 'JSON格式正确' }}
          </div>
        </div>

        <!-- 数组类型 -->
        <div v-else-if="form.dataType === 'array'" class="array-editor">
          <el-input
            v-model="arrayString"
            type="textarea"
            :rows="6"
            placeholder="请输入数组格式的数据，如：[1, 2, 3] 或 ['a', 'b', 'c']"
            @input="handleArrayInput"
          />
          <div class="array-validation" :class="{ error: arrayError }">
            {{ arrayError || '数组格式正确' }}
          </div>
        </div>

        <!-- 密码类型 -->
        <el-input
          v-else-if="form.dataType === 'password'"
          v-model="form.value"
          type="password"
          placeholder="请输入密码"
          maxlength="100"
          show-password
        />

        <!-- URL类型 -->
        <el-input
          v-else-if="form.dataType === 'url'"
          v-model="form.value"
          placeholder="请输入URL地址，如：https://example.com"
          maxlength="500"
        />

        <!-- 邮箱类型 -->
        <el-input
          v-else-if="form.dataType === 'email'"
          v-model="form.value"
          placeholder="请输入邮箱地址，如：<EMAIL>"
          maxlength="100"
        />

        <!-- 默认文本输入 -->
        <el-input
          v-else
          v-model="form.value"
          placeholder="请输入配置值"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :loading="saving"
          @click="handleSave"
        >
          {{ isEditing ? '更新' : '创建' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  config: {
    type: Object,
    default: () => ({})
  },
  isEditing: {
    type: Boolean,
    default: false
  },
  dataTypeOptions: {
    type: Array,
    default: () => []
  },
  categoryOptions: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:visible', 'save', 'close'])

// 响应式数据
const formRef = ref()
const saving = ref(false)
const jsonString = ref('')
const jsonError = ref('')
const arrayString = ref('')
const arrayError = ref('')

const form = reactive({
  key: '',
  value: '',
  description: '',
  category: 'general',
  dataType: 'string',
  enable: true
})

// 表单验证规则
const rules = {
  key: [
    { required: true, message: '请输入配置键', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/, message: '配置键只能包含字母、数字和下划线，且以字母开头', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入配置描述', trigger: 'blur' },
    { min: 5, max: 200, message: '长度在 5 到 200 个字符', trigger: 'blur' }
  ],
  category: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ],
  dataType: [
    { required: true, message: '请选择数据类型', trigger: 'change' }
  ],
  value: [
    { required: true, message: '请输入配置值', trigger: 'blur' }
  ]
}

// 计算属性
const renderedMarkdown = computed(() => {
  if (!form.value) return ''
  
  // 简单的Markdown渲染
  return form.value
    .replace(/\n/g, '<br>')
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/`(.*?)`/g, '<code>$1</code>')
    .replace(/^# (.*$)/gm, '<h1>$1</h1>')
    .replace(/^## (.*$)/gm, '<h2>$1</h2>')
    .replace(/^### (.*$)/gm, '<h3>$1</h3>')
})

// 监听配置变化
watch(() => props.config, (newConfig) => {
  if (newConfig && Object.keys(newConfig).length > 0) {
    Object.assign(form, {
      key: newConfig.key || '',
      value: newConfig.value || '',
      description: newConfig.description || '',
      category: newConfig.category || 'general',
      dataType: newConfig.dataType || 'string',
      enable: newConfig.enable !== false
    })
    
    // 处理JSON和数组类型的显示
    if (form.dataType === 'json' && typeof form.value === 'object') {
      jsonString.value = JSON.stringify(form.value, null, 2)
    } else if (form.dataType === 'array' && Array.isArray(form.value)) {
      arrayString.value = JSON.stringify(form.value, null, 2)
    }
  }
}, { immediate: true, deep: true })

// 监听对话框显示状态
watch(() => props.visible, (visible) => {
  if (visible && !props.isEditing) {
    // 新增时重置表单
    resetForm()
  }
})

// 方法
function resetForm() {
  Object.assign(form, {
    key: '',
    value: '',
    description: '',
    category: 'general',
    dataType: 'string',
    enable: true
  })
  jsonString.value = ''
  jsonError.value = ''
  arrayString.value = ''
  arrayError.value = ''
  
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

function handleDataTypeChange() {
  // 数据类型改变时重置值
  if (form.dataType === 'boolean') {
    form.value = true
  } else if (form.dataType === 'number') {
    form.value = 0
  } else if (form.dataType === 'json') {
    form.value = {}
    jsonString.value = '{}'
  } else if (form.dataType === 'array') {
    form.value = []
    arrayString.value = '[]'
  } else {
    form.value = ''
  }
}

function handleJsonInput() {
  try {
    const parsed = JSON.parse(jsonString.value)
    form.value = parsed
    jsonError.value = ''
  } catch (error) {
    jsonError.value = 'JSON格式错误: ' + error.message
  }
}

function handleArrayInput() {
  try {
    const parsed = JSON.parse(arrayString.value)
    if (Array.isArray(parsed)) {
      form.value = parsed
      arrayError.value = ''
    } else {
      arrayError.value = '必须是数组格式'
    }
  } catch (error) {
    arrayError.value = '数组格式错误: ' + error.message
  }
}

async function handleSave() {
  try {
    const valid = await formRef.value.validate()
    if (!valid) return
    
    // 验证JSON和数组格式
    if (form.dataType === 'json' && jsonError.value) {
      ElMessage.error('请修正JSON格式错误')
      return
    }
    
    if (form.dataType === 'array' && arrayError.value) {
      ElMessage.error('请修正数组格式错误')
      return
    }
    
    saving.value = true
    
    const configData = { ...form }
    
    // 处理特殊数据类型
    if (form.dataType === 'json') {
      configData.value = form.value
    } else if (form.dataType === 'array') {
      configData.value = form.value
    }
    
    emit('save', configData)
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    saving.value = false
  }
}

function handleClose() {
  emit('update:visible', false)
  emit('close')
}

// 暴露方法给父组件
defineExpose({
  resetForm,
  setSaving: (value) => { saving.value = value }
})
</script>

<style scoped>
.config-form {
  padding: 0 10px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.type-option,
.category-option {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.type-name,
.category-name {
  font-weight: 500;
  color: #303133;
}

.type-desc,
.category-desc {
  font-size: 12px;
  color: #909399;
}

.markdown-editor {
  width: 100%;
}

.markdown-preview {
  margin-top: 16px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.preview-title {
  background: #f5f7fa;
  padding: 8px 12px;
  font-size: 12px;
  color: #606266;
  border-bottom: 1px solid #dcdfe6;
}

.preview-content {
  padding: 12px;
  max-height: 200px;
  overflow-y: auto;
  background: white;
}

.preview-content h1,
.preview-content h2,
.preview-content h3 {
  margin: 0 0 8px 0;
  color: #303133;
}

.preview-content h1 {
  font-size: 18px;
}

.preview-content h2 {
  font-size: 16px;
}

.preview-content h3 {
  font-size: 14px;
}

.preview-content code {
  background: #f5f7fa;
  padding: 2px 4px;
  border-radius: 2px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
}

.json-editor,
.array-editor {
  width: 100%;
}

.json-validation,
.array-validation {
  margin-top: 8px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  background: #f0f9ff;
  color: #0ea5e9;
  border: 1px solid #bae6fd;
}

.json-validation.error,
.array-validation.error {
  background: #fef2f2;
  color: #ef4444;
  border-color: #fecaca;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-textarea__inner) {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-radio-group) {
  display: flex;
  gap: 20px;
}
</style>
