<template>
  <div class="config-value-display">
    <!-- 字符串类型 -->
    <div v-if="dataType === 'string'" class="value-string">
      <span class="value-text">{{ displayValue }}</span>
    </div>

    <!-- 多行文本类型 -->
    <div v-else-if="dataType === 'text'" class="value-text">
      <el-tooltip :content="value" placement="top" :disabled="!isTruncated">
        <span class="value-text truncated">{{ displayValue }}</span>
      </el-tooltip>
    </div>

    <!-- Markdown类型 -->
    <div v-else-if="dataType === 'markdown'" class="value-markdown">
      <el-tooltip placement="top">
        <template #content>
          <div class="markdown-preview" v-html="renderedMarkdown"></div>
        </template>
        <div class="markdown-indicator">
          <FileCodeIcon :size="14" />
          <span>Markdown ({{ value.length }} 字符)</span>
        </div>
      </el-tooltip>
    </div>

    <!-- 数字类型 -->
    <div v-else-if="dataType === 'number'" class="value-number">
      <el-tag type="success" size="small">{{ value }}</el-tag>
    </div>

    <!-- 布尔类型 -->
    <div v-else-if="dataType === 'boolean'" class="value-boolean">
      <el-tag :type="value ? 'success' : 'danger'" size="small">
        <CheckIcon v-if="value" :size="12" />
        <XIcon v-else :size="12" />
        {{ value ? '是' : '否' }}
      </el-tag>
    </div>

    <!-- JSON类型 -->
    <div v-else-if="dataType === 'json'" class="value-json">
      <el-tooltip placement="top">
        <template #content>
          <pre class="json-preview">{{ formattedJson }}</pre>
        </template>
        <div class="json-indicator">
          <BracesIcon :size="14" />
          <span>JSON ({{ Object.keys(parsedJson || {}).length }} 个属性)</span>
        </div>
      </el-tooltip>
    </div>

    <!-- 数组类型 -->
    <div v-else-if="dataType === 'array'" class="value-array">
      <el-tooltip placement="top">
        <template #content>
          <div class="array-preview">
            <div v-for="(item, index) in parsedArray" :key="index" class="array-item">
              {{ index }}: {{ typeof item === 'object' ? JSON.stringify(item) : item }}
            </div>
          </div>
        </template>
        <div class="array-indicator">
          <ListIcon :size="14" />
          <span>数组 ({{ (parsedArray || []).length }} 个元素)</span>
        </div>
      </el-tooltip>
    </div>

    <!-- 密码类型 -->
    <div v-else-if="dataType === 'password'" class="value-password">
      <div class="password-display">
        <LockIcon :size="14" />
        <span>{{ showPassword ? value : '••••••••' }}</span>
        <el-button
          type="text"
          size="small"
          @click="togglePassword"
        >
          <EyeIcon v-if="!showPassword" :size="12" />
          <EyeOffIcon v-else :size="12" />
        </el-button>
      </div>
    </div>

    <!-- URL类型 -->
    <div v-else-if="dataType === 'url'" class="value-url">
      <el-link :href="value" target="_blank" type="primary">
        <LinkIcon :size="14" />
        {{ displayValue }}
      </el-link>
    </div>

    <!-- 邮箱类型 -->
    <div v-else-if="dataType === 'email'" class="value-email">
      <el-link :href="`mailto:${value}`" type="primary">
        <MailIcon :size="14" />
        {{ value }}
      </el-link>
    </div>

    <!-- 未知类型 -->
    <div v-else class="value-unknown">
      <span class="value-text">{{ displayValue }}</span>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import {
  FileCode as FileCodeIcon,
  Check as CheckIcon,
  X as XIcon,
  Braces as BracesIcon,
  List as ListIcon,
  Lock as LockIcon,
  Eye as EyeIcon,
  EyeOff as EyeOffIcon,
  Link as LinkIcon,
  Mail as MailIcon
} from 'lucide-vue-next'

const props = defineProps({
  value: {
    type: [String, Number, Boolean, Object, Array],
    required: true
  },
  dataType: {
    type: String,
    required: true
  },
  maxLength: {
    type: Number,
    default: 50
  }
})

const showPassword = ref(false)

// 计算属性
const displayValue = computed(() => {
  if (props.value === null || props.value === undefined) {
    return '-'
  }
  
  const str = String(props.value)
  if (str.length > props.maxLength) {
    return str.substring(0, props.maxLength) + '...'
  }
  return str
})

const isTruncated = computed(() => {
  const str = String(props.value)
  return str.length > props.maxLength
})

const renderedMarkdown = computed(() => {
  if (props.dataType !== 'markdown' || !props.value) {
    return ''
  }
  
  // 简单的Markdown渲染（实际项目中可以使用markdown-it等库）
  return props.value
    .replace(/\n/g, '<br>')
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/`(.*?)`/g, '<code>$1</code>')
})

const parsedJson = computed(() => {
  if (props.dataType !== 'json') return null
  
  try {
    return typeof props.value === 'string' 
      ? JSON.parse(props.value) 
      : props.value
  } catch (error) {
    return null
  }
})

const formattedJson = computed(() => {
  if (!parsedJson.value) return ''
  return JSON.stringify(parsedJson.value, null, 2)
})

const parsedArray = computed(() => {
  if (props.dataType !== 'array') return null
  
  try {
    return Array.isArray(props.value) 
      ? props.value 
      : JSON.parse(props.value)
  } catch (error) {
    return null
  }
})

// 方法
function togglePassword() {
  showPassword.value = !showPassword.value
}
</script>

<style scoped>
.config-value-display {
  max-width: 100%;
}

.value-text {
  color: #606266;
  word-break: break-all;
}

.value-text.truncated {
  cursor: help;
}

.value-markdown .markdown-indicator,
.value-json .json-indicator,
.value-array .array-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #909399;
  font-size: 12px;
  cursor: help;
}

.markdown-preview {
  max-width: 300px;
  max-height: 200px;
  overflow: auto;
  white-space: pre-wrap;
  word-break: break-word;
}

.json-preview {
  max-width: 400px;
  max-height: 300px;
  overflow: auto;
  background: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.4;
}

.array-preview {
  max-width: 300px;
  max-height: 200px;
  overflow: auto;
}

.array-item {
  padding: 2px 0;
  font-size: 12px;
  border-bottom: 1px solid #eee;
}

.array-item:last-child {
  border-bottom: none;
}

.password-display {
  display: flex;
  align-items: center;
  gap: 4px;
}

.value-url .el-link,
.value-email .el-link {
  display: flex;
  align-items: center;
  gap: 4px;
}
</style>
