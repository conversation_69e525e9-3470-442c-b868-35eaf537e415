<template>
  <div class="config-management-container">
    <div class="page-header">
      <div class="header-left">
        <h1 class="page-title">
          <SettingsIcon :size="24" />
          配置管理
        </h1>
        <p class="page-description">管理系统配置参数和设置</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="refreshData">
          <template #icon>
            <RefreshCwIcon :size="16" />
          </template>
          刷新
        </el-button>
        <el-button type="success" @click="showCreateDialog">
          <template #icon>
            <PlusIcon :size="16" />
          </template>
          新增配置
        </el-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <div class="stat-card">
        <div class="stat-icon">
          <SettingsIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.total || 0 }}</div>
          <div class="stat-label">总配置数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon enabled">
          <CheckCircleIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.enabled || 0 }}</div>
          <div class="stat-label">已启用</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon disabled">
          <XCircleIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.disabled || 0 }}</div>
          <div class="stat-label">已禁用</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon categories">
          <FolderIcon :size="24" />
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.categories || 0 }}</div>
          <div class="stat-label">分类数</div>
        </div>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="filter-section">
      <div class="filter-row">
        <div class="filter-item">
          <el-input
            v-model="filters.keyword"
            placeholder="搜索配置键或描述"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <SearchIcon :size="16" />
            </template>
          </el-input>
        </div>
        <div class="filter-item">
          <el-select v-model="filters.category" placeholder="选择分类" clearable>
            <el-option label="全部分类" value="all" />
            <el-option
              v-for="category in categoryOptions"
              :key="category.value"
              :label="category.label"
              :value="category.value"
            />
          </el-select>
        </div>
        <div class="filter-item">
          <el-select v-model="filters.dataType" placeholder="选择数据类型" clearable>
            <el-option label="全部类型" value="all" />
            <el-option
              v-for="type in dataTypeOptions"
              :key="type.value"
              :label="type.label"
              :value="type.value"
            />
          </el-select>
        </div>
        <div class="filter-item">
          <el-select v-model="filters.enable" placeholder="启用状态">
            <el-option label="全部状态" value="all" />
            <el-option label="已启用" value="true" />
            <el-option label="已禁用" value="false" />
          </el-select>
        </div>
        <div class="filter-actions">
          <el-button type="primary" @click="handleSearch">
            <template #icon>
              <SearchIcon :size="16" />
            </template>
            搜索
          </el-button>
          <el-button @click="resetFilters">
            <template #icon>
              <RotateCcwIcon :size="16" />
            </template>
            重置
          </el-button>
        </div>
      </div>
    </div>

    <!-- 配置列表 -->
    <div class="config-list">
      <el-table
        v-loading="loading"
        :data="configList"
        stripe
        border
        style="width: 100%"
      >
        <el-table-column prop="key" label="配置键" min-width="200">
          <template #default="{ row }">
            <div class="config-key">
              <el-tag size="small" type="info">{{ row.key }}</el-tag>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="description" label="描述" min-width="200" />
        
        <el-table-column prop="dataType" label="数据类型" width="120">
          <template #default="{ row }">
            <el-tag
              :type="getDataTypeTagType(row.dataType)"
              size="small"
            >
              {{ getDataTypeLabel(row.dataType) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="category" label="分类" width="120">
          <template #default="{ row }">
            <el-tag
              :color="getCategoryColor(row.category)"
              size="small"
              effect="light"
            >
              {{ getCategoryLabel(row.category) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="value" label="配置值" min-width="200">
          <template #default="{ row }">
            <div class="config-value">
              <ConfigValueDisplay :value="row.value" :dataType="row.dataType" />
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="enable" label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.enable ? 'success' : 'danger'" size="small">
              {{ row.enable ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="updatedAt" label="更新时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.updatedAt) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button
                type="primary"
                size="small"
                @click="editConfig(row)"
              >
                <template #icon>
                  <EditIcon :size="14" />
                </template>
                编辑
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="deleteConfig(row)"
              >
                <template #icon>
                  <TrashIcon :size="14" />
                </template>
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Settings as SettingsIcon,
  CheckCircle as CheckCircleIcon,
  XCircle as XCircleIcon,
  Folder as FolderIcon,
  RefreshCw as RefreshCwIcon,
  Plus as PlusIcon,
  Search as SearchIcon,
  RotateCcw as RotateCcwIcon,
  Edit as EditIcon,
  Trash as TrashIcon
} from 'lucide-vue-next'

// 导入API和组件
import { 
  getConfigList, 
  getConfigStats, 
  getConfigTypes,
  deleteConfig as deleteConfigAPI 
} from '@/api/config.js'
import ConfigValueDisplay from './components/ConfigValueDisplay.vue'

// 响应式数据
const loading = ref(false)
const configList = ref([])
const stats = ref({})
const dataTypeOptions = ref([])
const categoryOptions = ref([])

const filters = reactive({
  category: 'all',
  dataType: 'all',
  enable: 'all',
  keyword: ''
})

const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
})

// 计算属性
const getDataTypeLabel = computed(() => {
  return (dataType) => {
    const option = dataTypeOptions.value.find(opt => opt.value === dataType)
    return option ? option.label : dataType
  }
})

const getCategoryLabel = computed(() => {
  return (category) => {
    const option = categoryOptions.value.find(opt => opt.value === category)
    return option ? option.label : category
  }
})

const getDataTypeTagType = computed(() => {
  return (dataType) => {
    const typeMap = {
      string: 'info',
      text: 'info',
      markdown: 'warning',
      number: 'success',
      boolean: 'primary',
      json: 'danger',
      array: 'danger',
      password: 'warning',
      url: 'info',
      email: 'info'
    }
    return typeMap[dataType] || 'info'
  }
})

const getCategoryColor = computed(() => {
  return (category) => {
    const option = categoryOptions.value.find(opt => opt.value === category)
    return option ? option.color : '#909399'
  }
})

// 方法
async function loadConfigList() {
  try {
    loading.value = true
    
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      ...filters
    }
    
    const result = await getConfigList(params)
    
    if (result && result.success) {
      configList.value = result.data.list || []
      pagination.total = result.data.total || 0
    } else {
      ElMessage.error(result?.message || '获取配置列表失败')
    }
  } catch (error) {
    console.error('获取配置列表失败:', error)
    ElMessage.error('获取配置列表失败')
  } finally {
    loading.value = false
  }
}

async function loadStats() {
  try {
    const result = await getConfigStats()
    if (result && result.success) {
      stats.value = result.data || {}
    }
  } catch (error) {
    console.error('获取配置统计失败:', error)
  }
}

async function loadConfigTypes() {
  try {
    const result = await getConfigTypes()
    if (result && result.success) {
      dataTypeOptions.value = result.data.dataTypes || []
      categoryOptions.value = result.data.categories || []
    }
  } catch (error) {
    console.error('获取配置类型失败:', error)
  }
}

function handleSearch() {
  pagination.page = 1
  loadConfigList()
}

function resetFilters() {
  filters.category = 'all'
  filters.dataType = 'all'
  filters.enable = 'all'
  filters.keyword = ''
  pagination.page = 1
  loadConfigList()
}

function handleSizeChange(size) {
  pagination.pageSize = size
  pagination.page = 1
  loadConfigList()
}

function handleCurrentChange(page) {
  pagination.page = page
  loadConfigList()
}

async function refreshData() {
  await Promise.all([loadConfigList(), loadStats()])
  ElMessage.success('数据已刷新')
}

function showCreateDialog() {
  // TODO: 实现创建配置对话框
  ElMessage.info('创建配置功能开发中...')
}

function editConfig(config) {
  // TODO: 实现编辑配置对话框
  ElMessage.info('编辑配置功能开发中...')
}

async function deleteConfig(config) {
  try {
    await ElMessageBox.confirm(
      `确定要删除配置 "${config.key}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const result = await deleteConfigAPI(config.key)
    
    if (result && result.success) {
      ElMessage.success('配置删除成功')
      loadConfigList()
      loadStats()
    } else {
      ElMessage.error(result?.message || '删除配置失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除配置失败:', error)
      ElMessage.error('删除配置失败')
    }
  }
}

function formatDateTime(dateTime) {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 生命周期
onMounted(async () => {
  await loadConfigTypes()
  await Promise.all([loadConfigList(), loadStats()])
})
</script>

<style scoped>
.config-management-container {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.page-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-description {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 20px;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 8px;
  background: #f0f9ff;
  color: #0ea5e9;
}

.stat-icon.enabled {
  background: #f0fdf4;
  color: #22c55e;
}

.stat-icon.disabled {
  background: #fef2f2;
  color: #ef4444;
}

.stat-icon.categories {
  background: #fefbeb;
  color: #f59e0b;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.filter-section {
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.filter-row {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.filter-item {
  min-width: 200px;
}

.filter-actions {
  display: flex;
  gap: 8px;
  margin-left: auto;
}

.config-list {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.config-key {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.config-value {
  max-width: 200px;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.pagination-wrapper {
  padding: 16px 20px;
  border-top: 1px solid #ebeef5;
  display: flex;
  justify-content: center;
}

@media (max-width: 768px) {
  .config-management-container {
    padding: 10px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .header-right {
    width: 100%;
    justify-content: flex-end;
  }

  .filter-row {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-item {
    min-width: auto;
  }

  .filter-actions {
    margin-left: 0;
    justify-content: center;
  }

  .stats-cards {
    grid-template-columns: 1fr;
  }
}
</style>
