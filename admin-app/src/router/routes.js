/**
 * 路由配置
 */

import Layout from '@/components/Layout/index.vue'

const routes = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/setup',
    name: 'Setup',
    component: () => import('@/views/Setup/index.vue'),
    meta: {
      title: '配置设置',
      hideInMenu: true
    }
  },
  {
    path: '/dashboard',
    component: Layout,
    meta: {
      title: '仪表板',
      icon: 'BarChart3',
      requiresAuth: true
    },
    children: [
      {
        path: '',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard/index.vue'),
        meta: {
          title: '仪表板'
        }
      }
    ]
  },
  {
    path: '/announcements',
    component: Layout,
    meta: {
      title: '公告管理',
      icon: 'Megaphone',
      requiresAuth: true
    },
    children: [
      {
        path: '',
        name: 'AnnouncementList',
        component: () => import('@/views/Announcements/index.vue'),
        meta: {
          title: '公告管理'
        }
      }
    ]
  },
  {
    path: '/users',
    component: Layout,
    meta: {
      title: '用户管理',
      icon: 'Users',
      requiresAuth: true
    },
    children: [
      {
        path: '',
        name: 'UserList',
        component: () => import('@/views/Users/<USER>'),
        meta: {
          title: '用户管理'
        }
      }
    ]
  },
  {
    path: '/feedback',
    component: Layout,
    meta: {
      title: '反馈管理',
      icon: 'MessageSquare',
      requiresAuth: true
    },
    children: [
      {
        path: '',
        name: 'FeedbackList',
        component: () => import('@/views/Feedback/index.vue'),
        meta: {
          title: '反馈管理'
        }
      }
    ]
  },
  {
    path: '/points',
    component: Layout,
    meta: {
      title: '积分管理',
      icon: 'Coins',
      requiresAuth: true
    },
    children: [
      {
        path: '',
        name: 'PointsManagement',
        component: () => import('@/views/Points/index.vue'),
        meta: {
          title: '积分管理'
        }
      }
    ]
  },
  {
    path: '/store',
    component: Layout,
    meta: {
      title: '商店管理',
      icon: 'ShoppingBag',
      requiresAuth: true
    },
    children: [
      {
        path: '',
        name: 'StoreManagement',
        component: () => import('@/views/Store/index.vue'),
        meta: {
          title: '商店管理'
        }
      }
    ]
  },
  {
    path: '/data-export',
    component: Layout,
    meta: {
      title: '数据导出',
      icon: 'Download',
      requiresAuth: true
    },
    children: [
      {
        path: '',
        name: 'DataExport',
        component: () => import('@/views/DataExport/index.vue'),
        meta: {
          title: '数据导出'
        }
      }
    ]
  },
  {
    path: '/checkin',
    component: Layout,
    meta: {
      title: '签到管理',
      icon: 'CalendarCheck',
      requiresAuth: true
    },
    children: [
      {
        path: '',
        name: 'CheckInManagement',
        component: () => import('@/views/CheckIn/index.vue'),
        meta: {
          title: '签到管理'
        }
      }
    ]
  },
  {
    path: '/friend-apps',
    component: Layout,
    meta: {
      title: '友情应用',
      icon: 'Link',
      requiresAuth: true
    },
    children: [
      {
        path: '',
        name: 'FriendAppsManagement',
        component: () => import('@/views/FriendApps/index.vue'),
        meta: {
          title: '友情应用'
        }
      }
    ]
  },
  {
    path: '/fishing-status',
    component: Layout,
    meta: {
      title: '摸鱼状态',
      icon: 'Fish',
      requiresAuth: true
    },
    children: [
      {
        path: '',
        name: 'FishingStatusManagement',
        component: () => import('@/views/FishingStatus/index.vue'),
        meta: {
          title: '摸鱼状态'
        }
      }
    ]
  },
  {
    path: '/cache',
    component: Layout,
    meta: {
      title: '缓存管理',
      icon: 'Database',
      requiresAuth: true
    },
    children: [
      {
        path: '',
        name: 'CacheManagement',
        component: () => import('@/views/Cache/index.vue'),
        meta: {
          title: '缓存管理'
        }
      }
    ]
  },
  {
    path: '/config-management',
    component: Layout,
    meta: {
      title: '配置管理',
      icon: 'Settings',
      requiresAuth: true
    },
    children: [
      {
        path: '',
        name: 'ConfigManagement',
        component: () => import('@/views/ConfigManagement/index.vue'),
        meta: {
          title: '配置管理'
        }
      }
    ]
  },
  {
    path: '/settings',
    component: Layout,
    meta: {
      title: '系统设置',
      icon: 'Cog',
      requiresAuth: true
    },
    children: [
      {
        path: '',
        name: 'SystemSettings',
        component: () => import('@/views/Settings/index.vue'),
        meta: {
          title: '系统设置'
        }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/Error/404.vue'),
    meta: {
      title: '页面不存在',
      hideInMenu: true
    }
  }
]

export default routes
