/**
 * API统一导出文件
 * 提供统一的API调用接口
 */

import { 
  callCloudFunction,
  callCloudFunctionWithRetry,
  callCloudFunctionSilent,
  batchCallCloudFunction,
  testConnection,
  refreshAccessToken,
  setWechatConfig,
  getWechatConfig,
  clearWechatConfig,
  isConfigValid,
  getTokenStatus,
  getAccessTokenInfo,
  needsTokenRefresh
} from './wechat-api.js'

/**
 * 管理端API调用函数
 * 这是一个别名函数，实际调用的是 callCloudFunction
 * @param {string} type - API类型
 * @param {Object} data - 请求数据
 * @param {Object} options - 选项
 * @returns {Promise<Object>} 响应结果
 */
export function callAdminAPI(type, data = {}, options = {}) {
  return callCloudFunction(type, data, options)
}

// 重新导出所有微信API函数
export {
  callCloudFunction,
  callCloudFunctionWithRetry,
  callCloudFunctionSilent,
  batchCallCloudFunction,
  testConnection,
  refreshAccessToken,
  setWechatConfig,
  getWechatConfig,
  clearWechatConfig,
  isConfigValid,
  getTokenStatus,
  getAccessTokenInfo,
  needsTokenRefresh
}

// 默认导出
export default {
  callAdminAPI,
  callCloudFunction,
  callCloudFunctionWithRetry,
  callCloudFunctionSilent,
  batchCallCloudFunction,
  testConnection,
  refreshAccessToken,
  setWechatConfig,
  getWechatConfig,
  clearWechatConfig,
  isConfigValid,
  getTokenStatus,
  getAccessTokenInfo,
  needsTokenRefresh
}
