/**
 * 配置管理API
 */

import { callAdminAPI } from './index.js'

/**
 * 创建配置
 * @param {Object} data - 配置数据
 * @returns {Promise} 创建结果
 */
export function createConfig(data) {
  return callAdminAPI('createConfig', data, { showSuccess: true })
}

/**
 * 更新配置
 * @param {Object} data - 更新数据
 * @returns {Promise} 更新结果
 */
export function updateConfig(data) {
  return callAdminAPI('updateConfig', data, { showSuccess: true })
}

/**
 * 删除配置
 * @param {string} key - 配置键
 * @returns {Promise} 删除结果
 */
export function deleteConfig(key) {
  return callAdminAPI('deleteConfig', { key }, { showSuccess: true })
}

/**
 * 获取配置列表（管理端）
 * @param {Object} params - 查询参数
 * @returns {Promise} 配置列表
 */
export function getConfigList(params = {}) {
  return callAdminAPI('getAllConfigsAdmin', params)
}

/**
 * 获取配置统计
 * @returns {Promise} 统计数据
 */
export function getConfigStats() {
  return callAdminAPI('getConfigStats')
}

/**
 * 批量更新配置
 * @param {Array} configs - 配置列表
 * @returns {Promise} 更新结果
 */
export function batchUpdateConfigs(configs) {
  return callAdminAPI('batchUpdateConfigs', { configs }, { showSuccess: true })
}

/**
 * 获取配置类型信息
 * @returns {Promise} 配置类型信息
 */
export function getConfigTypes() {
  return callAdminAPI('getConfigTypes')
}
